package firmwarefact

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	crand "crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"math/rand"
	"net"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/firmware"
	"github.com/flipped-aurora/gin-vue-admin/server/model/sangforapi"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 32字节的AES-256密钥
const aesKey = "Kj#9mP$2vN@5hX8qL&4wR!7cF3bE9nM6"

// 包内全局变量
var Details = []string{}

// EncryptPath 使用AES加密文件路径
func EncryptPath(path string) (string, error) {
	// 创建cipher
	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		return "", err
	}

	// 创建gcm
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 创建nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(crand.Reader, nonce); err != nil {
		return "", err
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(path), nil)

	// 转换为base64
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 提取 CVE 编号中的年份部分
func extractYearFromCVE(cveID string) int {
	// 假设 CVE 编号格式为 "CVE-YYYY-XXXXX"
	parts := strings.Split(cveID, "-")
	if len(parts) >= 2 {
		year, err := strconv.Atoi(parts[1]) // 尝试转换年份
		if err == nil {
			return year
		}
	}
	return 0 // 如果格式不对，返回 0 年
}

// ConvertFileToBase64 将文件转为 Base64 编码字符串
func ConvertFileToBase64(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 读取文件内容
	fileData, err := ioutil.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %v", err)
	}

	// 将文件内容转为 Base64 编码
	base64Encoded := base64.StdEncoding.EncodeToString(fileData)
	return base64Encoded, nil
}

// UploadFirmware 提交固件信息到指定接口
func putUploadFirmware(url string, payload map[string]interface{}) (map[string]interface{}, error) {
	// 将 payload 转换为 JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	// 创建 PUT 请求
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// 将响应解析为 map
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return response, nil
}

func randomString(length int) string {
	rand.Seed(time.Now().UnixNano())
	chars := []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	var b strings.Builder
	for i := 0; i < length; i++ {
		b.WriteRune(chars[rand.Intn(len(chars))])
	}
	return b.String()
}

func UploadFirmware(filePath string, device_name, ip string) (uid string, err error) {
	// 转换文件为 Base64
	base64Binary, err := ConvertFileToBase64(filePath)
	if err != nil {
		addDetail(fmt.Sprintf("将上传的固件文件转换为 Base64 时出错:", err))
		global.GVA_LOG.Error("将上传的固件文件转换为 Base64 时出错:", zap.Error(err))
		return
	}

	raq := []string{"cpu_architecture", "cve_lookup", "kernel_config", "software_components", "known_vulnerabilities", "file_hashes", "users_and_passwords"}

	translations := []string{
		"CPU架构",
		"CVE漏洞查询",
		"内核配置",
		"软件组件",
		"已知漏洞",
		"文件哈希值",
		"用户和密码",
	}
	addDetail(fmt.Sprintf("使用以下多个固件分析模块:%s", strings.Join(translations, " | ")))

	addDetail(fmt.Sprintf("构造上传固件的请求体"))

	fc := randomString(16)
	suz := strings.Split(device_name, ".")
	if len(suz) == 2 {
		fc = randomString(16) + "." + suz[1]
	}

	// 构造请求体
	payload := map[string]interface{}{
		"binary":                     base64Binary,
		"device_class":               "cimer_device_class", // 设备的类别，用于描述设备的类型或用途。例如，路由器、摄像头、物联网设备等
		"device_name":                device_name,          // 设备的具体名称或型号，标识该设备的独特信息
		"device_part":                "cimer_device_part",  // 固件对应的设备部分。如果固件只针对某个特定组件，可以在这里说明。例如，主板、网络接口等
		"file_name":                  fc,
		"release_date":               "2024-10-01",
		"requested_analysis_systems": raq,
		"tags":                       "CSG-IOT",
		"vendor":                     "cimer_vendor",
		"version":                    "",
	}

	// 上传固件
	addDetail(fmt.Sprintf("上传固件文件"))
	url := fmt.Sprintf("http://%s/rest/firmware", ip) // 替换为实际 URL
	response, err := putUploadFirmware(url, payload)
	if err != nil {
		addDetail(fmt.Sprintf("上传固件时出错：", err))
		global.GVA_LOG.Error("上传固件时出错： %v\n", zap.Error(err))
		return
	}

	// 判断状态值并处理
	status, ok := response["status"].(float64)
	if !ok {
		addDetail(fmt.Sprintf("状态解析失败：status 不是数字类型"))
		global.GVA_LOG.Error("状态解析失败：status 不是数字类型 %v\n", zap.Error(err))
		return "", fmt.Errorf("status 类型错误")
	}

	if int(status) == 1 {
		errorMessage, _ := response["error_message"].(string) // 安全转换
		addDetail(fmt.Sprintf("上传固件时出错：%s", errorMessage))
		global.GVA_LOG.Error("上传固件时出错： %v\n", zap.Error(err))
		return "", fmt.Errorf("error")
	}

	addDetail(fmt.Sprintf("上传固件成功，获取 UID：%s", response["uid"].(string)))
	// 获取 UID
	uid = response["uid"].(string)
	return
}

// 通用的 get 方法
func HttpGet(url string, headers map[string]string) (string, error) {
	// Create a new HTTP client
	client := &http.Client{}
	// Create a new GET request
	req, err := http.NewRequest("GET", url, nil) // No request body for GET
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}
	// Add headers to the request, if any
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	// Perform the request
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	// Read the response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}
	// Check the response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected HTTP status: %d - %s", resp.StatusCode, body)
	}

	return string(body), nil
}

// 查看运行状态，判断是否运行结束，以及其他相关信息
// 得看 current_analyses 是否存在 UID，若不存在了则扫描完成了，
// 假如超过N小时后，还没扫完则，直接去查数据，查完就删了这个任务
// 返回值：0=成功完成, 1=失败, 2=超时但有数据
func CheckStatus(uid string, ip string, startTimeS string, db *gorm.DB, fuid uint) (status int) {
	addDetail(fmt.Sprintf(">>>固件开始分析，请等待"))
	url := fmt.Sprintf("http://%s/rest/status", ip)
	// 这里需要思考下给一个阈值，若超过多久时间则，默认成功获取
	// 设置为12小时
	startTime := time.Now()
	lastOutputTime := time.Now() // 用于记录上次输出时间
	threshold := 3 * time.Hour   // 设置时间阈值为 3 小时
	checkNum := 0
	for {
		if checkNum > 90 {
			addDetail(">>>超过 15分钟，任务仍未获取到检查任务数据，提取失败")
			global.GVA_LOG.Info(">>>超过 15分钟，仍未获取到检测任务数据，任务失败")
			return 1 // 返回失败状态
		}
		// 检查是否超过时间阈值
		if time.Since(startTime) > threshold {
			addDetail(">>>超过扫描阈值时间，打断检测的进程，但会尝试获取已有数据")
			global.GVA_LOG.Info(">>>超过阈值时间，打断检测的进程，返回超时状态")
			return 2 // 返回超时状态，表示有部分数据可用
		}
		// 发起 GET 请求获取状态信息
		response, err := HttpGet(url, nil)
		if err != nil {
			addDetail(fmt.Sprintf("获取运行状态出错: %v", err))
			global.GVA_LOG.Error("获取运行状态出错", zap.Error(err))
			// 网络错误，返回失败状态
			return 1
		}
		// 解析 JSON 响应
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(response), &data); err != nil {
			addDetail(fmt.Sprintf("解析 JSON 失败: %v", err))
			global.GVA_LOG.Error("解析 JSON 失败", zap.Error(err))
			// JSON解析错误，返回失败状态
			return 1
		}
		// 进入 system_status -> backend -> analysis
		systemStatus, ok := data["system_status"].(map[string]interface{})
		if !ok {
			addDetail(fmt.Sprintf("system_status 数据结构无效"))
			global.GVA_LOG.Info("system_status 数据结构无效")
			// 数据结构错误，返回失败状态
			return 1
		}
		backend, ok := systemStatus["backend"].(map[string]interface{})
		if !ok {
			addDetail(fmt.Sprintf("backend 数据结构无效"))
			global.GVA_LOG.Info("backend 数据结构无效")
			// 数据结构错误，返回失败状态
			return 1
		}
		analysis, ok := backend["analysis"].(map[string]interface{})
		if !ok {
			addDetail(fmt.Sprintf("analysis 数据结构无效"))
			global.GVA_LOG.Info("analysis 数据结构无效")
			// 数据结构错误，返回失败状态
			return 1
		}

		// 检查当前正在分析的任务（current_analyses）
		currentAnalyses, ok := analysis["current_analyses"].(map[string]interface{})

		if ok {
			if info, exists := currentAnalyses[uid].(map[string]interface{}); exists {
				// 这里展示进度
				totalCount, totalCountWithDuplicates, unpackedCount := 0, 0, 0
				if total_count, ok := info["total_count"].(float64); ok {
					totalCount = int(total_count)
					//fmt.Printf("分析任务总的数量: %d\n", int(total_count))
				}
				if total_count_with_duplicates, ok := info["total_count_with_duplicates"].(float64); ok {
					totalCountWithDuplicates = int(total_count_with_duplicates)
					//fmt.Printf("已完成的任务数量（包括重复项）: %d\n", int(total_count_with_duplicates))
				}
				if unpacked_count, ok := info["unpacked_count"].(float64); ok {
					unpackedCount = int(unpacked_count)
					//fmt.Printf("解包数量: %d\n", int(unpacked_count))
				}

				analyses := fmt.Sprintf(
					"分析任务总数: %d, 已完成的分析数量(包括重复项): %d (进度: %.2f%%), 解包数量: %d (进度: %.2f%%)",
					totalCount,
					totalCountWithDuplicates,
					(float64(totalCountWithDuplicates)/float64(totalCount))*100,
					unpackedCount,
					(float64(unpackedCount)/float64(totalCount))*100,
				)

				// 检查切片是否为空，避免索引越界
				if len(Details) > 0 {
					lastElement := Details[len(Details)-1]
					if strings.Contains(lastElement, "分析任务总数") {
						// 删除最后一个元素
						Details = Details[:len(Details)-1]
					}
				}

				addDetail(analyses)

			} else {
				checkNum++
				addDetail(fmt.Sprintf("任务不在检测运行列表中，正在检查结束任务列表..."))
				global.GVA_LOG.Info("UID 不在 current_analyses 中，正在检查 recently_finished_analyses...")
			}
		}

		// 检查最近完成的分析任务（recently_finished_analyses）
		recentlyFinished, ok := analysis["recently_finished_analyses"].(map[string]interface{})
		if ok {
			if _, exists := recentlyFinished[uid]; exists {
				addDetail(fmt.Sprintf("固件分析已完成！！！"))
				global.GVA_LOG.Info("固件分析已完成。")
				// 分析完成，返回成功状态
				return 0
			}
		}

		// 每隔10分钟输出一次状态
		if time.Since(lastOutputTime) >= 10*time.Minute {
			addDetail("===> 每隔10分钟，先解析现有分析出的固件信息数据，但任务还未结束，数据并不完整！")
			var result_end json.RawMessage
			err, res_firm := GetResult(uid, ip, startTimeS, "")
			if err != nil {
				addDetail("===> 本次固件分析数据解析失败")
			}
			jsonBytes, err := json.Marshal(res_firm)
			if err != nil {
				addDetail("===> 本次固件分析数据解析失败")
			}
			result_end = jsonBytes
			details_end := strings.Join(Details, "\n")
			err_ := UpdateFirmwareResult(db, fuid, result_end, details_end)
			if err_ != nil {
				global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
			}
			lastOutputTime = time.Now() // 重置计时器
		}

		// 等待 10 秒后再次检查状态
		time.Sleep(10 * time.Second)
	}
}
}

// 删除已经成功运行的数据 - 改进版本，确保可靠删除
func deleteFire(uid string, ip string) {
	if uid == "" || ip == "" {
		addDetail("删除任务参数无效，跳过删除操作")
		global.GVA_LOG.Warn("删除任务参数无效", zap.String("uid", uid), zap.String("ip", ip))
		return
	}

	url := fmt.Sprintf("http://%s/admin/delete/%s", ip, uid)
	maxRetries := 3
	retryDelay := 2 * time.Second

	addDetail(fmt.Sprintf("开始删除残留的任务 (UID: %s)...", uid))

	for attempt := 1; attempt <= maxRetries; attempt++ {
		_, err := HttpGet(url, nil)
		if err != nil {
			if attempt < maxRetries {
				addDetail(fmt.Sprintf("删除任务失败 (第%d次尝试): %v，%v秒后重试...", attempt, err, int(retryDelay.Seconds())))
				global.GVA_LOG.Warn("删除任务失败，准备重试",
					zap.String("uid", uid),
					zap.Int("attempt", attempt),
					zap.Error(err))
				time.Sleep(retryDelay)
				retryDelay *= 2 // 指数退避
				continue
			} else {
				addDetail(fmt.Sprintf("删除任务最终失败 (已重试%d次): %v", maxRetries, err))
				global.GVA_LOG.Error("删除任务最终失败",
					zap.String("uid", uid),
					zap.Int("totalAttempts", maxRetries),
					zap.Error(err))
				return
			}
		}

		// 删除成功
		addDetail(fmt.Sprintf("任务删除成功 (第%d次尝试)", attempt))
		global.GVA_LOG.Info("任务删除成功", zap.String("uid", uid), zap.Int("attempt", attempt))
		return
	}
}

// ContainsOS 检查输入字符串是否包含已知的操作系统名称
func containsOS(input string, commonFirmwareOS []string) bool {
	// 转换输入为小写以进行不区分大小写的比较
	input = strings.ToLower(input)

	// 遍历所有已知的操作系统名称
	for _, fros := range commonFirmwareOS {
		if strings.Contains(input, fros) {
			return true
		}
	}
	return false
}

// 整理返回的数据进行一个符合格式的 json，这里会很复杂

// 查看结果
func GetResult(uid, ip, startTime, address string) (err error, res StatusResponse) {
	// 获取扫描的结果
	url := fmt.Sprintf("http://%s/rest/firmware/%s?summary=true", ip, uid)
	response, err := HttpGet(url, nil)
	if err != nil {
		addDetail(fmt.Sprintf("获取扫描结果失败:", err))
		global.GVA_LOG.Error("获取扫描结果失败: %v\n", zap.Error(err))
		return
	}

	var resFact FirmwareData
	err = json.Unmarshal([]byte(response), &resFact)
	if err != nil {
		addDetail(fmt.Sprintf("解析 JSON 失败:", err))
		global.GVA_LOG.Error("Error converting to JSON: %v\n", zap.Error(err))
		return
	}

	//var res StatusResponse

	res.Time.StartTime = startTime
	formattedTime := time.Now().Format("2006-01-02 15:04:05")
	res.Time.EndTime = formattedTime // 当前时间为结束时间
	res.Time.UsedTime = ""           // 耗时这个再说

	rsa_dir := "/usr/src/app/uploads/firmware/" + address // 存放路径，这边是固定的

	res.Binfile.Dir, _ = EncryptPath(rsa_dir)

	global.GVA_LOG.Info(fmt.Sprintf("RSA 加密后的路径：%s", res.Binfile.Dir))

	if resFact.Firmware.Analysis.FileHashes.Result != nil {
		// 检查 Result 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.FileHashes.Result.(map[string]interface{}); ok {
			// 检查 "md5" 字段是否存在并且是 string
			if md5, ok := resultMap["md5"].(string); ok {
				res.Binfile.MD5 = md5
			} else {
				// "md5" 字段不存在或类型不是 string，设置默认值
				res.Binfile.MD5 = ""
			}
		} else {
			// Result 存在但不是 map[string]interface{}，设置默认值
			res.Binfile.MD5 = ""
		}
	} else {
		// Result 字段不存在，设置默认值
		res.Binfile.MD5 = ""
	}

	res.Binfile.Name = resFact.Firmware.MetaData.DeviceName // TODO：这里需要一开始就定义好

	//// 转换为 MB (1 MB = 1024 * 1024 bytes)
	//mb := float64(resFact.Firmware.MetaData.Size) / (1024 * 1024)
	//// 格式化结果为字符串，保留两位小数
	//res.Binfile.Size = fmt.Sprintf("%.2f MB", mb)

	res.Binfile.Size = resFact.Firmware.MetaData.Size

	// 一些硬件信息
	if resFact.Firmware.Analysis.DeviceTree.Summary != nil {
		// 检查 summ 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.DeviceTree.Summary.(map[string]interface{}); ok {
			res.Binfile.Manufacturer = concatSummary(resultMap)
		}
	}

	commonFirmwareOS := []string{
		// 嵌入式 Linux 系统
		"linux",
		"openwrt",
		"buildroot",
		"yocto",
		"openembedded",
		"uclinux",
		"windriver linux",
		// 实时操作系统
		"freertos",
		"vxworks",
		"qnx",
		"threadx",
		"integrity",
		"nucleus",
		"ucos", // 原 µc/os
		"rtx",
		"zephyr",
		"rtems",
		// 移动设备系统
		"android",
		// 网络设备系统
		"cisco ios",
		"huawei vrp",
		"dd-wrt",
		"pfSense",
		// 物联网系统
		"contiki",
		"riot",
		"mbed os",
		"liteos",
		"harmonyos",
		// 小型嵌入式系统
		"tinyos",
		"ecos",
		"pikeos",
	}

	// OS信息
	oss := []string{}
	// 内核信息提取
	factkernelinfo := ""
	if resFact.Firmware.Analysis.SoftwareComponents.Summary != nil {
		// 检查 summ 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.SoftwareComponents.Summary.(map[string]interface{}); ok {
			for key := range resultMap {
				isOK := containsOS(key, commonFirmwareOS)
				if isOK {
					oss = append(oss, key)
				}
				// 提取出内核相关信息
				if strings.Contains(strings.ToLower(key), "kernel") {
					factkernelinfo = key
				}
			}
		} else {
			// Result 存在但不是 map[string]interface{}，设置默认值
			res.SystemInfo.OS = ""
		}
	}
	if len(oss) > 0 {
		res.SystemInfo.OS = strings.Join(oss, " | ")
	}

	// CPU 架构，这里合并起来
	if resFact.Firmware.Analysis.CPUArchitecture.Result != nil {
		// 检查 summ 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.CPUArchitecture.Summary.(map[string]interface{}); ok {
			res.SystemInfo.CPU = concatSummary(resultMap)
		} else {
			// Result 存在但不是 map[string]interface{}，设置默认值
			res.SystemInfo.CPU = ""
		}
	} else {
		// Result 字段不存在，设置默认值
		res.SystemInfo.CPU = ""
	}

	// 固件的镜像类型，，这里需要从 FILE type 进行分析
	if resFact.Firmware.Analysis.FileType.Result != nil {
		// 检查 summ 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.FileType.Result.(map[string]interface{}); ok {
			res.SystemInfo.ImageType = concatSummaryWithValues(resultMap)
		} else {
			// Result 存在但不是 map[string]interface{}，设置默认值
			res.SystemInfo.ImageType = ""
		}
	} else {
		// Result 字段不存在，设置默认值
		res.SystemInfo.ImageType = ""
	}

	// factkernelinfo = "Linux Kernel 4.9.37"
	// 使用正则表达式提取版本号
	re := regexp.MustCompile(`\d+\.\d+\.\d+`)
	version := re.FindString(factkernelinfo)
	if version != "" {
		res.KernelVersion = version
	}

	res.KernelInfo = factkernelinfo // 简单的获取内核信息

	// 运行结果
	res.RunningReuslt = 1 // 这里不知道啥意思

	// 提取的文件信息
	res.ExtractedFileInfo.KnownNumberOfFiles = resFact.Firmware.MetaData.TotalFilesInFirmware

	// 下面就是漏洞信息了 这边就比较复杂了
	// var cve Vulnerability
	if resFact.Firmware.Analysis.CVELookup.Summary != nil {
		// 检查 Summary 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.CVELookup.Summary.(map[string]interface{}); ok {
			for _, rawValue := range resultMap {
				// 检查 rawValue 是否是 []interface{} 类型
				if values, ok := rawValue.([]interface{}); ok && len(values) > 0 {
					// 获取第一个文件的 ID，并断言为字符串
					if fileID, ok := values[0].(string); ok {
						cveURL := fmt.Sprintf("http://%s/rest/analysis/%s/cve_lookup", ip, fileID)
						cveResponse, err_ := HttpGet(cveURL, nil)
						if err_ != nil {
							addDetail(fmt.Sprintf("获取CVE文件漏洞风险信息失败:", err_))
							continue
						}
						var vulnerabilities AnalysisCveResponse
						err = json.Unmarshal([]byte(cveResponse), &vulnerabilities)
						if err != nil {
							addDetail(fmt.Sprintf("CVE文件详情，解析 JSON 失败:", err))
							global.GVA_LOG.Error("CVE文件详情，解析 JSON 失败: %v\n", zap.Error(err))
							return
						}

						// 遍历所有的 CVE 漏洞
						for cve_name, vuln := range vulnerabilities.Analysis.Result.CVEResults {
							// 版本漏洞有时候太多了
							for k, v := range vuln {
								var cve Vulnerability
								cve.Type = "[" + k + "] " + cve_name + " 版本漏洞"
								cve.ID = k

								// 检查是否为内核漏洞
								isKernelVuln := strings.Contains(strings.ToLower(cve_name), "kernel")

								// 对于内核漏洞，只处理 2021 年及之后的 CVE 漏洞
								if isKernelVuln {
									year := extractYearFromCVE(k) // 提取年份
									if year < 2021 {
										continue // 跳过2021年之前的内核漏洞
									}
								}

								if v.Score3 != "" {
									cve.Severity = parseScore(v.Score3)
								} else {
									cve.Severity = parseScore(v.Score2) // 回退到 Score2
								}
								cve.AffectedVersion = v.CPEVersion
								cve.Description = cve_name + "该组件版本存在漏洞风险，详情查看:" + "https://nvd.nist.gov/vuln/detail/" + k
								cve.FixSuggestion = "建议优先升级到最新安全版本，应用厂商提供的补丁；如无法立即修复，可采取临时措施（如限制访问、关闭高危功能）降低风险，同时加强系统安全配置和监控。"

								if isKernelVuln {
									res.Kernelvulnerablities = append(res.Kernelvulnerablities, cve)
								} else {
									res.Programvulnerablities = append(res.Programvulnerablities, cve)
								}

							}
						}

					}
				}
			}
		}
	}

	//var vulnerabilities_know Root
	// 对与一些已知的漏洞进行分析！
	if resFact.Firmware.Analysis.KnownVulnerabilities.Summary != nil {
		// 检查 Summary 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.KnownVulnerabilities.Summary.(map[string]interface{}); ok {
			for _, rawValue := range resultMap {
				// 检查 rawValue 是否是 []interface{} 类型
				if values, ok := rawValue.([]interface{}); ok && len(values) > 0 {
					// 获取第一个文件的 ID，并断言为字符串
					if fileID, ok := values[0].(string); ok {
						cveURL := fmt.Sprintf("http://%s/rest/analysis/%s/known_vulnerabilities", ip, fileID)
						cveResponse_, err_1 := HttpGet(cveURL, nil)
						if err_1 != nil {
							addDetail(fmt.Sprintf("获取已知漏洞文件漏洞风险信息失败::", err_1))
							continue
						}
						var vulnerabilities_know Root
						err = json.Unmarshal([]byte(cveResponse_), &vulnerabilities_know)
						if err != nil {
							addDetail(fmt.Sprintf("已知漏洞详情，解析 JSON 失败:", err))
							global.GVA_LOG.Error("已知漏洞详情，解析 JSON 失败: %v\n", zap.Error(err))
						}

						// 遍历所有的 CVE 漏洞
						for cve_name, vuln := range vulnerabilities_know.Analysis.Result {
							var cvekn Vulnerability
							cvekn.Type = "[原理漏洞] " + cve_name
							cvekn.ID = ""

							if vuln.Score == "high" {
								cvekn.Severity = 9
							} else if vuln.Score == "medium" {
								cvekn.Severity = 6
							} else if vuln.Score == "low" {
								cvekn.Severity = 3
							} else {
								cvekn.Severity = 0
							}

							cvekn.Description = vuln.Description
							cvekn.FixSuggestion = "建议应用厂商提供的补丁；如无法立即修复，可采取临时措施（如限制访问、关闭高危功能）降低风险，同时加强系统安全配置和监控。"

							res.Programvulnerablities = append(res.Programvulnerablities, cvekn)
						}
					}
				}
			}
		}
	}

	if resFact.Firmware.Analysis.UsersAndPasswords.Summary != nil {
		// 检查 Summary 是否是 map[string]interface{}
		if resultMap, ok := resFact.Firmware.Analysis.UsersAndPasswords.Summary.(map[string]interface{}); ok {
			for _, rawValue := range resultMap {
				// 检查 rawValue 是否是 []interface{} 类型
				if values, ok := rawValue.([]interface{}); ok && len(values) > 0 {
					// 获取第一个文件的 ID，并断言为字符串
					if fileID, ok := values[0].(string); ok {
						cveURL := fmt.Sprintf("http://%s/rest/analysis/%s/users_and_passwords", ip, fileID)
						//fmt.Println(cveURL)
						cveResponse, err_ := HttpGet(cveURL, nil)
						if err_ != nil {
							addDetail(fmt.Sprintf("获取用户名密码漏洞风险信息失败:", err_))
							continue
						}
						var vulnerabilitiesUP UserPasswords
						err = json.Unmarshal([]byte(cveResponse), &vulnerabilitiesUP)
						if err != nil {
							addDetail(fmt.Sprintf("用户名密码文件详情，解析 JSON 失败:", err))
							global.GVA_LOG.Error("已知漏洞详情，解析 JSON 失败: %v\n", zap.Error(err))
							return
						}
						for cve_name, vuln := range vulnerabilitiesUP.Analysis.Result {
							var cveup Vulnerability
							cveup.Type = "[用户信息风险] " + cve_name
							cveup.ID = ""
							cveup.Severity = 6
							passw := ""
							if vuln.Cracked {
								passw = "密码破解成功:" + vuln.Password
								cveup.Severity = 9
							} else {
								passw = "密码未破解成功"
							}
							cveup.Description = "用户信息与密码哈希:" + vuln.Entry + "｜" + passw
							cveup.FixSuggestion = "固件中存在用户账户信息泄露风险, 建议用户修改密码策略，使用更安全的加密算法（如 SHA256+Salt）存储密码，并检查是否存在弱口令账户"
							res.Programvulnerablities = append(res.Programvulnerablities, cveup)
						}
					}
				}
			}
		}
	}

	// 去重
	res.Kernelvulnerablities = RemoveDuplicateByIDAndType(res.Kernelvulnerablities)
	res.Programvulnerablities = RemoveDuplicateByIDAndType(res.Programvulnerablities)

	global.GVA_LOG.Info("固件分析数据解析完成",
		zap.Int("kernelVulns", len(res.Kernelvulnerablities)),
		zap.Int("programVulns", len(res.Programvulnerablities)))

	addDetail(fmt.Sprintf("成功解析获取分析的数据，内核漏洞:%d个，程序漏洞:%d个",
		len(res.Kernelvulnerablities), len(res.Programvulnerablities)))

	// 信息泄露的漏洞也可以展示出来，后面再搞

	//// 将结构体转换为格式化的 JSON 字符串
	//jsonBytes, err := json.MarshalIndent(res, "", "    ") // "    " 表示 4 空格缩进
	//if err != nil {
	//	fmt.Println("Error converting to JSON:", err)
	//	return
	//}
	//
	//// 输出格式化的 JSON 字符串
	//fmt.Println(string(jsonBytes))

	return

}

// RemoveDuplicateByIDAndType 基于 ID + Type 去重
func RemoveDuplicateByIDAndType(vulnerabilities []Vulnerability) []Vulnerability {
	uniqueMap := make(map[string]Vulnerability)

	for _, v := range vulnerabilities {
		// 使用 "ID + Type" 作为 map 的 key
		key := v.ID + "|" + v.Type
		uniqueMap[key] = v // map 本身会自动覆盖相同 key 的数据
	}

	// 将去重后的数据转换回切片
	var uniqueList []Vulnerability
	for _, v := range uniqueMap {
		uniqueList = append(uniqueList, v)
	}
	return uniqueList
}

func parseScore(score string) int {
	// 尝试将字符串转换为浮点数
	if floatScore, err := strconv.ParseFloat(score, 64); err == nil {
		// 向下取整
		if floatScore < 10 {
			return int(math.Floor(floatScore))
		}
	}
	// 如果解析失败或分数大于等于 10，返回一个默认值
	return 0
}

// 拼接 Summary  键名
func concatSummary(summary map[string]interface{}) string {
	// 如果 summary 为空，直接返回空字符串
	if len(summary) == 0 {
		return ""
	}
	// 使用 strings.Builder 拼接字符串
	var builder strings.Builder
	for key := range summary {
		if builder.Len() > 0 {
			builder.WriteString(" | ")
		}
		// 替换换行符为空格
		key = strings.ReplaceAll(key, "\n", " ")
		builder.WriteString(key)
	}
	// 返回拼接后的结果
	return builder.String()
}

func concatSummaryWithValues(summary map[string]interface{}) string {
	// 如果 summary 为空，直接返回空字符串
	if len(summary) == 0 {
		return ""
	}

	// 使用 strings.Builder 拼接字符串
	var builder strings.Builder
	for key, value := range summary {
		if builder.Len() > 0 {
			builder.WriteString(" | ")
		}
		// 替换换行符为空格
		key = strings.ReplaceAll(key, "\n", " ")

		// 将值转为字符串，如果值为 nil，则表示为空
		var valueStr string
		if value != nil {
			valueStr = fmt.Sprintf("%v", value)
		} else {
			valueStr = "nil"
		}
		// 拼接键和值
		builder.WriteString(fmt.Sprintf("%s: %s", key, valueStr))
	}
	// 返回拼接后的结果
	return builder.String()
}

// UpdateFirmwareStatus 更新固件任务状态
func UpdateFirmwareEnd(db *gorm.DB, uid uint, status uint, result json.RawMessage, detail string) error {
	// 使用 gorm 的链式调用来构建 SQL 查询
	// 使用 time.Truncate 将时间截断为毫秒级别
	now := time.Now().Truncate(time.Millisecond)
	err := db.Model(&firmware.Firmware{}).Where("id = ?", uid).Updates(map[string]interface{}{
		"status":      status,
		"result":      result,
		"detail":      detail,
		"update_time": now,
	}).Error

	if err != nil {
		global.GVA_LOG.Error("更新数据错误 %v\n", zap.Error(err))
	}

	return nil
}

// UpdateFirmwareStatus 更新固件任务状态
func UpdateFirmwareResult(db *gorm.DB, uid uint, result json.RawMessage, detail string) error {
	// 使用 gorm 的链式调用来构建 SQL 查询
	err := db.Model(&firmware.Firmware{}).Where("id = ?", uid).Updates(map[string]interface{}{
		"result": result,
		"detail": detail,
	}).Error

	if err != nil {
		global.GVA_LOG.Error("更新数据错误 %v\n", zap.Error(err))
	}

	return nil
}

// UpdateFirmwareStatus 更新固件任务状态
func UpdateFirmwareStatus(db *gorm.DB, uid uint, status uint, detail string) error {
	// 使用 gorm 的链式调用来构建 SQL 查询
	err := db.Model(&firmware.Firmware{}).Where("id = ?", uid).Updates(map[string]interface{}{
		"status": status,
		"detail": detail,
		//"update_time": time.Now(),
	}).Error

	if err != nil {
		global.GVA_LOG.Error("更新数据错误 %v\n", zap.Error(err))
	}

	return nil
}

// AsyncTask 每隔30秒更新状态
func AsyncTask(ctx context.Context, db *gorm.DB, uid uint) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒触发
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done(): // 检查是否收到取消信号
			global.GVA_LOG.Info("异步任务已结束")
			return
		case <-ticker.C:
			// 更新任务状态
			details_end := strings.Join(Details, "\n")
			err := UpdateFirmwareStatus(db, uid, 5, details_end)
			if err != nil {
				global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
				return
			}
			global.GVA_LOG.Info("任务状态已更新")
		}
	}
}

func addDetail(msg string) {
	// 格式化当前时间
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	// 添加带时间戳的消息
	Details = append(Details, fmt.Sprintf("[%s] %s", currentTime, msg))
}

// sendSyslogMessage 通用的syslog消息发送函数
func sendSyslogMessage(targetAddr string, jsonData []byte, programName string, description string) error {
	// 建立UDP连接
	conn, err := net.Dial("udp", targetAddr)
	if err != nil {
		global.GVA_LOG.Error("建立UDP连接失败",
			zap.String("target", targetAddr),
			zap.String("programName", programName),
			zap.Error(err))
		return fmt.Errorf("failed to connect to %s: %v", targetAddr, err)
	}
	defer conn.Close()

	// 设置连接超时时间
	writeTimeout := time.Duration(global.GVA_CONFIG.FirmwareAnalysis.SyslogServer.WriteTimeout) * time.Second
	conn.SetWriteDeadline(time.Now().Add(writeTimeout))

	// 构造syslog格式的消息
	// 格式: <优先级>时间戳 主机名 程序名: JSON数据
	timestamp := time.Now().Format("2006-01-02T15:04:05Z07:00")
	hostname := "firmware-analyzer"
	priority := 134 // facility=16(local0), severity=6(info)

	syslogMessage := fmt.Sprintf("<%d>%s %s %s: %s",
		priority, timestamp, hostname, programName, string(jsonData))

	// 发送UDP数据包
	_, err = conn.Write([]byte(syslogMessage))
	if err != nil {
		global.GVA_LOG.Error("发送UDP数据包失败",
			zap.String("programName", programName),
			zap.String("target", targetAddr),
			zap.Int("dataSize", len(syslogMessage)),
			zap.Error(err))
		return fmt.Errorf("failed to send UDP packet: %v", err)
	}

	// 只在数据较大或出错时记录详细日志
	if len(syslogMessage) > 10000 || strings.Contains(programName, "chunk") {
		global.GVA_LOG.Debug("发送数据到syslog服务器",
			zap.String("target", targetAddr),
			zap.String("programName", programName),
			zap.Int("dataSize", len(syslogMessage)))
	}

	return nil
}

// sendSecurityEventsViaSyslog 通过UDP发送安全事件到指定的syslog服务器
func sendSecurityEventsViaSyslog(targetAddr string, events []sangforapi.SecurityEvent) error {
	if len(events) == 0 {
		return nil
	}

	// 分批发送事件，避免单个UDP包过大
	batchSize := 10 // 每批发送10个事件
	totalBatches := (len(events) + batchSize - 1) / batchSize
	successCount := 0
	failCount := 0

	global.GVA_LOG.Info("开始发送安全事件",
		zap.String("target", targetAddr),
		zap.Int("totalEvents", len(events)),
		zap.Int("batches", totalBatches))

	for i := 0; i < totalBatches; i++ {
		start := i * batchSize
		end := (i + 1) * batchSize
		if end > len(events) {
			end = len(events)
		}

		batchEvents := events[start:end]

		// 将事件批次转换为JSON格式
		jsonData, err := json.Marshal(batchEvents)
		if err != nil {
			global.GVA_LOG.Error("序列化事件数据失败", zap.Int("batch", i+1), zap.Error(err))
			failCount++
			continue
		}

		// 发送syslog消息
		description := fmt.Sprintf("安全事件批次 %d/%d (%d条事件)", i+1, totalBatches, len(batchEvents))
		err = sendSyslogMessage(targetAddr, jsonData, "security-events", description)
		if err != nil {
			global.GVA_LOG.Error("发送安全事件批次失败", zap.Int("batch", i+1), zap.Error(err))
			failCount++
			continue
		}
		successCount++

		// 批次间添加小延迟，避免网络拥塞
		if i < totalBatches-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	global.GVA_LOG.Info("安全事件发送完成",
		zap.Int("成功", successCount),
		zap.Int("失败", failCount),
		zap.Int("总批次", totalBatches))

	global.GVA_LOG.Info("完成安全事件UDP发送",
		zap.String("target", targetAddr),
		zap.Int("totalEvents", len(events)))

	return nil
}

// FirmwareChunk 固件数据分片结构
type FirmwareChunk struct {
	ChunkID     string `json:"chunk_id"`     // 分片唯一标识
	ChunkIndex  int    `json:"chunk_index"`  // 当前分片索引（从0开始）
	TotalChunks int    `json:"total_chunks"` // 总分片数
	FirmwareID  uint   `json:"firmware_id"`  // 固件ID
	Data        string `json:"data"`         // 分片数据（Base64编码）
	Timestamp   int64  `json:"timestamp"`    // 时间戳
}

// sendFirmwareInfoViaSyslog 通过UDP分片发送固件信息到指定的syslog服务器
func sendFirmwareInfoViaSyslog(targetAddr string, firmwareID uint, db *gorm.DB) error {
	// 查询固件数据
	var firmwareInfo firmware.Firmware
	if err := db.Model(&firmware.Firmware{}).Where("id = ?", firmwareID).First(&firmwareInfo).Error; err != nil {
		addDetail(fmt.Sprintf("===> 查询固件数据失败: %v", err))
		global.GVA_LOG.Error("查询firmware表数据失败", zap.Error(err))
		return err
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(firmwareInfo)
	if err != nil {
		addDetail(fmt.Sprintf("===> 固件数据序列化失败: %v", err))
		global.GVA_LOG.Error("序列化firmware表数据失败", zap.Error(err))
		return err
	}

	// 检查数据大小，决定是否需要分片
	// 从配置文件读取分片设置
	maxChunkSize := global.GVA_CONFIG.FirmwareAnalysis.ChunkTransfer.MaxChunkSize
	directSendThreshold := global.GVA_CONFIG.FirmwareAnalysis.ChunkTransfer.DirectSendThreshold
	dataSize := len(jsonData)

	if dataSize <= directSendThreshold {
		// 数据较小，直接发送
		addDetail(fmt.Sprintf("===> 固件数据直接发送 (%d bytes)", dataSize))
		description := fmt.Sprintf("固件信息 (ID: %d, Size: %d)", firmwareID, dataSize)
		err = sendSyslogMessage(targetAddr, jsonData, "firmware-info", description)
		if err != nil {
			addDetail(fmt.Sprintf("===> 发送固件数据失败: %v", err))
			return err
		}
		addDetail("===> 固件数据发送成功")
		global.GVA_LOG.Info("固件信息发送完成", zap.Uint("firmwareID", firmwareID), zap.Int("size", dataSize))
		return nil
	}

	// 数据过大，需要分片发送
	totalChunks := (dataSize + maxChunkSize - 1) / maxChunkSize
	chunkID := fmt.Sprintf("firmware_%d_%d", firmwareID, time.Now().Unix())

	addDetail(fmt.Sprintf("===> 固件数据分片发送: %d bytes -> %d 个分片", dataSize, totalChunks))
	global.GVA_LOG.Info("开始分片发送固件信息",
		zap.Uint("firmwareID", firmwareID),
		zap.Int("totalSize", dataSize),
		zap.Int("totalChunks", totalChunks),
		zap.String("chunkID", chunkID))

	// 分片发送
	successCount := 0
	for i := 0; i < totalChunks; i++ {
		start := i * maxChunkSize
		end := start + maxChunkSize
		if end > dataSize {
			end = dataSize
		}

		// 创建分片数据
		chunkData := jsonData[start:end]
		chunk := FirmwareChunk{
			ChunkID:     chunkID,
			ChunkIndex:  i,
			TotalChunks: totalChunks,
			FirmwareID:  firmwareID,
			Data:        base64.StdEncoding.EncodeToString(chunkData),
			Timestamp:   time.Now().Unix(),
		}

		// 序列化分片
		chunkJSON, err := json.Marshal(chunk)
		if err != nil {
			addDetail(fmt.Sprintf("===> 分片 %d/%d 序列化失败: %v", i+1, totalChunks, err))
			global.GVA_LOG.Error("分片序列化失败", zap.Error(err), zap.Int("chunkIndex", i))
			continue
		}

		// 发送分片
		description := fmt.Sprintf("固件信息分片 %d/%d (ID: %d, ChunkID: %s)", i+1, totalChunks, firmwareID, chunkID)
		err = sendSyslogMessage(targetAddr, chunkJSON, "firmware-chunk", description)
		if err != nil {
			addDetail(fmt.Sprintf("===> 发送分片 %d/%d 失败: %v", i+1, totalChunks, err))
			global.GVA_LOG.Error("发送分片失败", zap.Error(err), zap.Int("chunkIndex", i))
			return err
		}

		successCount++
		// 只在关键节点记录进度（每10个分片或最后一个）
		if i%10 == 0 || i == totalChunks-1 {
			addDetail(fmt.Sprintf("===> 分片发送进度: %d/%d", i+1, totalChunks))
		}

		// 分片间添加配置的延迟，避免网络拥塞
		if i < totalChunks-1 {
			chunkDelay := time.Duration(global.GVA_CONFIG.FirmwareAnalysis.ChunkTransfer.ChunkDelay) * time.Millisecond
			time.Sleep(chunkDelay)
		}
	}

	addDetail(fmt.Sprintf("===> 分片发送完成: %d/%d 成功", successCount, totalChunks))
	global.GVA_LOG.Info("固件信息分片发送完成",
		zap.Uint("firmwareID", firmwareID),
		zap.Int("successChunks", successCount),
		zap.Int("totalChunks", totalChunks),
		zap.String("chunkID", chunkID))
	return nil
}

// Exr_Firm 固件分析主函数
func Exr_Firm(filePath, originalName, address string, factIpPort string, fuid uint, assetID json.RawMessage) {
	// 初始化一下包的全局变量
	Details = []string{}

	db := global.GVA_DB
	var uid string // 声明uid变量，确保在defer中可以访问

	// 添加panic恢复机制，确保任何异常都不会导致任务状态卡住
	defer func() {
		if r := recover(); r != nil {
			addDetail(fmt.Sprintf("===> 固件分析过程中发生严重错误: %v", r))
			global.GVA_LOG.Error("固件分析过程中发生panic", zap.Any("panic", r))

			// 确保设置失败状态（删除任务由另一个defer处理）
			details_end := strings.Join(Details, "\n")
			err := UpdateFirmwareStatus(db, fuid, 2, details_end)
			if err != nil {
				global.GVA_LOG.Error("panic恢复时状态更新失败", zap.Error(err))
			}
		}
	}()

	addDetail("===> 开始进行固件的上传与分析")

	// 异步任务,用于更新任务的状态
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // 确保函数结束时取消异步任务
	go AsyncTask(ctx, db, fuid)

	// 固件文件路径
	//filePath := "/Users/<USER>/Desktop/网上下载的一些固件/bin" // 替换为实际路径
	//factIpPort := "************:9016"
	global.GVA_LOG.Info("===> 开始进行固件的上传与分析")
	var err error
	uid, err = UploadFirmware(filePath, originalName, factIpPort)
	if err != nil {
		addDetail("===> 固件上传失败")
		details_end := strings.Join(Details, "\n")
		cancel() // 取消异步任务
		err = UpdateFirmwareStatus(db, fuid, 2, details_end)
		if err != nil {
			global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
		}
		return
	}

	addDetail(fmt.Sprintf("===> 固件上传成功 UID: %s", uid))

	global.GVA_LOG.Info(fmt.Sprintf("UID:%s", uid))

	// 确保在函数结束时删除任务（无论成功还是失败）
	defer func() {
		if uid != "" {
			addDetail("===> 执行最终清理：删除远程任务")
			deleteFire(uid, factIpPort)
		}
	}()

	// 这里要搞一个 开始分析的时间
	startTime := time.Now().Format("2006-01-02 15:04:05")
	isFa := CheckStatus(uid, factIpPort, startTime, db, fuid) // 这个会堵塞 直到运行结束
	if isFa {
		addDetail("===> 固件分析失败")
		details_end := strings.Join(Details, "\n")
		cancel() // 取消异步任务

		err = UpdateFirmwareStatus(db, fuid, 2, details_end)
		if err != nil {
			global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
		}
		return
	}

	// UID := "205ce1c24cb6b6ffa9e0ff0db09150383e0f98e940c123d9e862a12dd159e163_16187492"
	err, res_firm := GetResult(uid, factIpPort, startTime, address)
	if err != nil {
		addDetail("===> 固件分析数据解析失败")
		details_end := strings.Join(Details, "\n")
		cancel() // 取消异步任务
		err = UpdateFirmwareStatus(db, fuid, 2, details_end)
		if err != nil {
			global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
		}
		return
	}

	var result_end json.RawMessage

	jsonBytes, err := json.Marshal(res_firm)
	if err != nil {
		global.GVA_LOG.Error("Error converting to JSON: %v\n", zap.Error(err))
		details_end := strings.Join(Details, "\n")
		cancel() // 取消异步任务
		err = UpdateFirmwareStatus(db, fuid, 2, details_end)
		if err != nil {
			global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
		}
		return
	}

	result_end = jsonBytes

	// 先取消异步任务，避免状态被覆盖
	cancel()

	// 解析资产ID列表
	var assids []string
	err = json.Unmarshal(assetID, &assids)
	if err != nil {
		addDetail("===> 资产 ID 解析失败")
		global.GVA_LOG.Error("资产 ID 解析失败: %v\n", zap.Error(err))
		details_end := strings.Join(Details, "\n")
		// 即使资产ID解析失败，固件分析本身是成功的，但整体任务失败
		err = UpdateFirmwareEnd(db, fuid, 2, result_end, details_end)
		if err != nil {
			global.GVA_LOG.Error("状态更新失败: %v\n", zap.Error(err))
		}
		return
	}

	// ========== 构建安全事件数据 ==========
	var events []sangforapi.SecurityEvent
	now := time.Now().Unix()

	// 为每个资产构建程序漏洞事件
	for _, asset := range assids {
		for _, v := range res_firm.Programvulnerablities {
			// 根据漏洞严重程度设置可靠性等级
			reliability := 0
			switch {
			case v.Severity >= 7:
				reliability = 3
			case v.Severity > 3 && v.Severity < 7:
				reliability = 2
			default:
				reliability = 1
			}
			dam := v.Description + " " + "影响版本:" + v.AffectedVersion
			securityEventFirmware := sangforapi.SecurityEvent{
				RiskType:        3,
				AttackState:     "0",
				FirstTime:       now,
				IP:              "",
				InfoSecurity:    "固件风险",
				InfoSecuritySub: "固件程序漏洞",
				Brief:           v.Type,
				DetectEngine:    "固件风险检测模块",
				AssetID:         asset,
				EventKey:        "",
				SipID:           "",
				Damage:          dam,
				Priority:        1,
				Reliability:     reliability,
				Role:            2,
				Emergency:       "normal",
				Principle:       dam,
				LastTime:        now,
				EventDes:        v.Type,
				Stage:           7,
				Solution:        v.FixSuggestion,
				IsCompliance:    1,
				EventType:       "告警事件",
			}
			events = append(events, securityEventFirmware)
		}

		// 为每个资产构建内核漏洞事件
		for _, v := range res_firm.Kernelvulnerablities {
			// 根据漏洞严重程度设置可靠性等级和紧急程度
			reliability := 0
			em := "normal"
			switch {
			case v.Severity >= 7:
				reliability = 3
				em = "emergent"
			case v.Severity > 3 && v.Severity < 7:
				reliability = 2
				em = "important"
			default:
				reliability = 1
			}
			securityEventFirmware := sangforapi.SecurityEvent{
				RiskType:        3,
				AttackState:     "0",
				FirstTime:       now,
				IP:              "",
				InfoSecurity:    "固件风险",
				InfoSecuritySub: "固件程序漏洞",
				Brief:           v.Type,
				DetectEngine:    "固件风险检测模块",
				AssetID:         asset,
				EventKey:        "",
				SipID:           "",
				Damage:          v.Description,
				Priority:        1,
				Reliability:     reliability,
				Role:            2,
				Emergency:       em,
				Principle:       v.Description,
				LastTime:        now,
				EventDes:        v.Type,
				Stage:           7,
				Solution:        v.FixSuggestion,
				IsCompliance:    1,
				EventType:       "告警事件",
			}
			events = append(events, securityEventFirmware)
		}
	}

	// ========== 通过UDP发送安全事件到syslog服务器 ==========
	targetAddr := global.GVA_CONFIG.FirmwareAnalysis.SyslogServer.TargetAddr

	if len(events) > 0 {
		addDetail(fmt.Sprintf("===> 开始发送 %d 条安全事件", len(events)))

		// 调用UDP发送函数
		err = sendSecurityEventsViaSyslog(targetAddr, events)
		if err != nil {
			addDetail(fmt.Sprintf("===> 安全事件发送失败: %v", err))
			global.GVA_LOG.Error("安全事件发送失败", zap.Error(err))
			// UDP发送失败不影响整体任务成功，只记录错误
		} else {
			addDetail(fmt.Sprintf("===> 安全事件发送成功: %d 条", len(events)))
		}
	} else {
		addDetail("===> 无安全事件需要发送")
	}

	// ========== 最终任务完成状态更新 ==========
	// 添加任务完成的详情信息
	addDetail("===> 固件分析任务完全完成，包括数据存储和事件发送")

	// 获取最终的详情信息
	final_details := strings.Join(Details, "\n")

	// 最终更新任务状态为完成 (状态码1表示成功完成)
	// 使用UpdateFirmwareEnd确保result和status都正确更新
	err = UpdateFirmwareEnd(db, fuid, 1, result_end, final_details)
	if err != nil {
		global.GVA_LOG.Error("最终任务状态更新失败: %v\n", zap.Error(err))
	} else {
		global.GVA_LOG.Info("固件分析任务完全完成", zap.Uint("firmwareID", fuid))
	}

	// ========== 发送firmware表数据到syslog ==========
	err = sendFirmwareInfoViaSyslog(targetAddr, fuid, db)
	if err != nil {
		global.GVA_LOG.Error("发送固件信息失败", zap.Error(err))
		// 发送失败不影响整体任务成功，只记录错误
	}
}
