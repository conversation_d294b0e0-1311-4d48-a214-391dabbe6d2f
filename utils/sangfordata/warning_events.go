/*
简单的实现思路：
通过对最近 30天的安全事件进行分析，处理历史事件数据，并根据分析结果生成预警

生成以下参数：

	    表示威胁预警结果，主要包含以下字段：
		•	预警IP地址（IP）
		•	风险等级（RiskLevel）
		•	威胁发生概率（Probability）
		•	预测可能的攻击类型（PredictedTypes）
		•	最近相关事件（RecentEvents）
		•	建议措施（Suggestions）
	    •	未来7天风险概率

实现逻辑：

1.	按IP分组事件（groupEventsByIP）
  - 根据安全事件的IP地址进行分组，并只考虑分析窗口(30天)内的事件。

2.	对每个IP进行威胁分析（analyzeThreatsByIP）
  - 根据分组后的事件，计算风险指标（RiskMetrics），如：
  - 事件总数、高风险事件数、攻击成功率等。
  - 决定是否需要生成预警（shouldGenerateWarning）。
  - 如果需要，生成预警信息（ThreatWarning），并补充详细信息。

风险指标计算（calculateRiskMetrics）
  - 分析一组事件的各种关键指标，例如：
  - 高风险事件数：Reliability和Priority都为3的事件。
  - 攻击成功率：攻击结果为"成功"或"失陷"的比例。
  - 最近事件频率：过去7天内的事件平均每天发生次数。
  - 攻击阶段进展：判断事件的攻击阶段是否呈现递增趋势。

预警判断条件（shouldGenerateWarning）
  - 满足以下任一条件即可生成预警：
  - 存在高风险事件。
  - 平均威胁等级较高。
  - 攻击成功率较高。
  - 攻击阶段有进展。
  - 最近事件频率达到一定水平。

风险等级计算（calculateRiskLevel）
  - 通过多个指标综合计算风险分数，并将其划分为：
  - 低风险（1）
  - 中风险（2）
  - 高风险（3）

威胁发生概率计算（calculateProbability）
  - 根据以下因素估算发生概率：
  - 攻击成功率
  - 平均威胁等级
  - 最近事件频率
  - 如果攻击阶段有进展，则概率额外提高。

补充预警详情（enrichWarningDetails）
  - 预测可能的攻击类型（predictAttackTypes）。
  - 列出最近的相关事件（getRecentEvents）。
  - 提供防护建议（generateSuggestions）。
  - 计算未来7天的风险概率（calculateFutureRiskProbability）。
*/
package sangfordata

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/sangforapi"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// ThreatWarning 表示行为检测威胁预警结果
type ThreatWarning struct {
	IP              string   `json:"ip"`                // 预警 IP
	WarningTime     int64    `json:"warning_time"`      // 预警时间
	EventSummary    string   `json:"event_summary"`     // 事件简述
	RiskLevel       int      `json:"risk_level"`        // 1-低风险 2-中风险 3-高风险
	Probability     float64  `json:"probability"`       // 威胁发生概率
	PredictedTypes  []string `json:"predicted_types"`   // 预测可能发生的攻击类型
	RecentEvents    []string `json:"recent_events"`     // 最近的相关事件
	Suggestions     []string `json:"suggestions"`       // 建议措施
	FutureRiskLevel int      `json:"future_risk_level"` // 未来7天风险等级 0=>低 1=>中 2=>高"
	RiskType        int      `json:"risk_type"`         // 1=> 行为预警  2=>威胁预警
}

// ThreatAnalyzer 威胁分析器
type ThreatAnalyzer struct {
	historicalEvents []sangforapi.SecurityEvent
	analysisWindow   int   // 分析窗口(天)
	currentTime      int64 // 当前时间戳
}

// NewThreatAnalyzer 创建威胁分析器实例
func NewThreatAnalyzer(events []sangforapi.SecurityEvent, analysisWindow int) *ThreatAnalyzer {
	return &ThreatAnalyzer{
		historicalEvents: events,
		analysisWindow:   analysisWindow,
		currentTime:      time.Now().Unix(),
	}
}

// GenerateWarnings 生成威胁预警
func (ta *ThreatAnalyzer) GenerateWarnings() []ThreatWarning {
	var warnings []ThreatWarning

	// 1. 按IP分组分析
	ipGroups := ta.groupEventsByIP()

	// 2. 对每个IP进行威胁分析
	for ip, events := range ipGroups {
		if warning := ta.analyzeThreatsByIP(ip, events); warning != nil {
			warnings = append(warnings, *warning)
		}
	}

	// 3. 按风险等级排序
	sort.Slice(warnings, func(i, j int) bool {
		return warnings[i].RiskLevel > warnings[j].RiskLevel
	})

	return warnings
}

// groupEventsByIP 按IP分组事件
func (ta *ThreatAnalyzer) groupEventsByIP() map[string][]sangforapi.SecurityEvent {
	groups := make(map[string][]sangforapi.SecurityEvent)

	// 只分析分析窗口内的事件
	// windowStart := ta.currentTime - int64(ta.analysisWindow*24*3600)

	for _, event := range ta.historicalEvents {

		groups[event.IP] = append(groups[event.IP], event)
	}

	return groups
}

// analyzeThreatsByIP 分析每个个IP的威胁情况
func (ta *ThreatAnalyzer) analyzeThreatsByIP(ip string, events []sangforapi.SecurityEvent) *ThreatWarning {
	// 计算风险指标
	riskMetrics := ta.calculateRiskMetrics(events)

	// 判断是否需要预警
	if !ta.shouldGenerateWarning(riskMetrics) {
		return nil
	}

	// 生成预警
	warning := &ThreatWarning{
		IP:          ip,
		RiskLevel:   ta.calculateRiskLevel(riskMetrics),
		Probability: ta.calculateProbability(riskMetrics),
		WarningTime: time.Now().Unix(),
	}

	// 设置RiskType
	hasThreatEvent := false
	for _, event := range events {
		if event.RiskType == 2 {
			hasThreatEvent = true
			break
		}
	}
	if hasThreatEvent {
		warning.RiskType = 2 // 威胁预警
	} else {
		warning.RiskType = 1 // 行为预警
	}

	// 补充预警详情
	ta.enrichWarningDetails(warning, events, riskMetrics)

	return warning
}

// RiskMetrics 风险指标
type RiskMetrics struct {
	EventCount         int     // 事件总数
	HighRiskCount      int     // 高风险事件数
	MidRiskCount       int     // 中风险事件数
	AverageReliability float64 // 平均威胁等级
	AttackSuccess      float64 // 攻击成功率
	AssetCriticality   float64 // 资产重要性
	StageProgression   bool    // 攻击阶段是否呈现进展
	RecentFrequency    float64 // 最近事件频率
}

// calculateRiskMetrics 计算风险指标
func (ta *ThreatAnalyzer) calculateRiskMetrics(events []sangforapi.SecurityEvent) RiskMetrics {
	metrics := RiskMetrics{}

	for _, event := range events {
		// 累计事件总数
		metrics.EventCount++

		// 统计高风险事件
		if event.Reliability == 3 || event.Priority == 3 {
			metrics.HighRiskCount++
		} else if event.Reliability == 2 || event.Priority == 2 {
			metrics.MidRiskCount++
		}

		// 计算平均威胁等级
		metrics.AverageReliability += float64(event.Reliability)

		// 统计攻击成功率
		if event.AttackState == "2" || event.AttackState == "3" {
			metrics.AttackSuccess++
		}

		// 资产重要性评估优化
		switch event.Emergency {
		case "emergent":
			metrics.AssetCriticality += 1.0
		case "important":
			metrics.AssetCriticality += 0.8
		case "normal":
			metrics.AssetCriticality += 0.5
		}

	}

	if metrics.EventCount == 0 {
		return metrics // 返回默认值，避免后续计算
	}

	// 计算平均值
	if metrics.EventCount > 0 {
		metrics.AverageReliability /= float64(metrics.EventCount)
		metrics.AttackSuccess /= float64(metrics.EventCount)
		metrics.AssetCriticality /= float64(metrics.EventCount)
	}

	// 计算最近事件频率
	metrics.RecentFrequency = ta.calculateRecentFrequency(events)

	// 判断攻击阶段进展
	metrics.StageProgression = ta.detectStageProgression(events)

	return metrics
}

// calculateRecentFrequency 计算最近事件频率
func (ta *ThreatAnalyzer) calculateRecentFrequency(events []sangforapi.SecurityEvent) float64 {
	if len(events) < 2 {
		return 0
	}

	// 按时间排序
	sort.Slice(events, func(i, j int) bool {
		return events[i].LastTime < events[j].LastTime
	})

	// 计算最近N天的事件数量
	recentCount := 0

	weekAgo := ta.currentTime - int64(ta.analysisWindow)*24*3600

	for _, event := range events {
		if event.LastTime >= weekAgo {
			recentCount++
		}
	}

	return float64(recentCount) / float64(ta.analysisWindow) // 日平均事件数
}

// detectStageProgression 检测攻击阶段进展
func (ta *ThreatAnalyzer) detectStageProgression(events []sangforapi.SecurityEvent) bool {
	if len(events) < 2 {
		return false
	}

	// 按时间排序
	sort.Slice(events, func(i, j int) bool {
		return events[i].LastTime < events[j].LastTime
	})

	// 检查最近的事件是否显示攻击阶段的进展
	lastStage := events[len(events)-1].Stage
	prevStage := events[len(events)-2].Stage

	return lastStage > prevStage
}

// shouldGenerateWarning 判断是否需要生成预警
func (ta *ThreatAnalyzer) shouldGenerateWarning(metrics RiskMetrics) bool {
	// 符合以下任一条件则生成预警:
	return metrics.HighRiskCount > 0 || // 存在高风险事件
		metrics.AverageReliability >= 2.5 || // 平均威胁等级较高
		metrics.AttackSuccess >= 0.3 || // 攻击成功率较高
		metrics.StageProgression || // 攻击阶段呈现进展
		metrics.RecentFrequency >= 1.0 || // 日均事件数达到1次以上
		metrics.MidRiskCount >= 3 // 中风险事件数超过3个
}

// calculateRiskLevel 计算风险等级
func (ta *ThreatAnalyzer) calculateRiskLevel(metrics RiskMetrics) int {
	score := 0.0

	// 综合多个指标计算分数
	score += float64(metrics.HighRiskCount) * 0.3 // 高风险事件权重
	score += metrics.AverageReliability * 0.2     // 平均威胁等级权重
	score += metrics.AttackSuccess * 0.2          // 攻击成功率权重
	score += metrics.AssetCriticality * 0.15      // 资产重要性权重
	score += metrics.RecentFrequency * 0.15       // 事件频率权重

	if metrics.StageProgression {
		score *= 1.2 // 攻击阶段进展时提高分数
	}

	// 转换为风险等级
	if score >= 0.7 {
		return 3 // 高风险
	} else if score >= 0.4 {
		return 2 // 中风险
	}
	return 1 // 低风险
}

// calculateProbability 计算威胁发生概率
//func (ta *ThreatAnalyzer) calculateProbability(metrics RiskMetrics) float64 {
//	// 基于历史攻击成功率和当前风险指标估算概率
//	probability := metrics.AttackSuccess * 0.4
//	probability += (metrics.AverageReliability / 3.0) * 0.3
//	probability += math.Min(metrics.RecentFrequency/5.0, 1.0) * 0.3
//
//	if metrics.StageProgression {
//		probability *= 1.2 // 攻击阶段进展时提高概率
//	}
//
//	return math.Min(probability, 1.0)
//}

// calculateProbability 计算威胁发生概率
func (ta *ThreatAnalyzer) calculateProbability(metrics RiskMetrics) float64 {
	// 定义权重
	const (
		weightAttackSuccess    = 0.4
		weightReliability      = 0.3
		weightRecentFrequency  = 0.3
		stageProgressionFactor = 1.2 // 攻击阶段进展的额外加权
	)

	// 归一化函数：确保指标值在 [0,1] 范围内
	normalize := func(value, max float64) float64 {
		return math.Min(value/max, 1.0)
	}

	// 各项指标归一化处理
	normalizedAttackSuccess := normalize(metrics.AttackSuccess, 1.0)     // 攻击成功率本身已是百分比
	normalizedReliability := normalize(metrics.AverageReliability, 3.0)  // 平均威胁等级（1-3）
	normalizedRecentFrequency := normalize(metrics.RecentFrequency, 5.0) // 每日事件频率（合理上限为5）

	// 组合各指标的加权概率
	probability := normalizedAttackSuccess*weightAttackSuccess +
		normalizedReliability*weightReliability +
		normalizedRecentFrequency*weightRecentFrequency

	// 考虑攻击阶段进展的额外权重
	if metrics.StageProgression {
		probability *= stageProgressionFactor
	}

	// 确保概率不超过 1.0
	probability = math.Min(probability, 1.0)

	// 四舍五入到小数点后两位
	roundedProbability := math.Round(probability*100) / 100

	return roundedProbability
}

// 关键预警事件
func (ta *ThreatAnalyzer) generateEventSummary(recentEvents []sangforapi.SecurityEvent) string {
	if len(recentEvents) == 0 {
		return "无相关事件"
	}

	// 统计事件频率
	eventFreq := make(map[string]int)
	for _, event := range recentEvents {
		eventFreq[event.InfoSecuritySub]++
	}

	// 找出频率最高的事件
	var mostFrequentEvent string
	maxFreq := 0
	for event, freq := range eventFreq {
		if freq > maxFreq {
			mostFrequentEvent = event
			maxFreq = freq
		}
	}

	return mostFrequentEvent
}

// enrichWarningDetails 补充预警详情
func (ta *ThreatAnalyzer) enrichWarningDetails(warning *ThreatWarning, events []sangforapi.SecurityEvent, metrics RiskMetrics) {
	// 1. 预测可能的攻击类型
	warning.PredictedTypes = ta.predictAttackTypes(events)

	// 2. 添加最近相关事件
	warning.RecentEvents = ta.getRecentEvents(events)

	// 3. 生成建议措施
	warning.Suggestions = ta.generateSuggestions(events, metrics)

	warning.EventSummary = ta.generateEventSummary(events)

	// 4. 收集可能受影响的资产
	//warning.AffectedAssets = ta.collectAffectedAssets(events)

	// 5. 设置预警持续时间
	// warning.WarningDuration = ta.calculateWarningDuration(metrics)

	// 计算并设置未来7天风险概率
	// warning.FutureRiskProbability = ta.calculateFutureRiskProbability(metrics)

	// 根据不同风险等级生成不同的建议
	switch warning.RiskLevel {
	case 2: // 中风险
		warning.Suggestions = append(warning.Suggestions,
			"对于中风险事件，建议进行常规监控，定期检查系统日志，确保基础安全防护措施到位",
		)
	}

	// 根据RiskType生成不同的建议
	if warning.RiskType == 1 { // 行为预警
		warning.Suggestions = append(warning.Suggestions, "建议监控相关行为，加强访问控制")
	} else if warning.RiskType == 2 { // 威胁预警
		warning.Suggestions = append(warning.Suggestions, "建议进行威胁响应，采取应急措施")
	}

}

// predictAttackTypes 预测可能的攻击类型
func (ta *ThreatAnalyzer) predictAttackTypes(events []sangforapi.SecurityEvent) []string {
	// 统计历史攻击类型频率
	typeFreq := make(map[string]int)
	for _, event := range events {
		if event.InfoSecurity != "" {
			typeFreq[event.InfoSecurity]++
		}
	}

	// 选择出现频率较高的类型
	var types []string
	for t, freq := range typeFreq {
		if freq >= 2 { // 出现2次以上的攻击类型
			types = append(types, t)
		}
	}

	// 如果没有预测出类型，则取最新一次事件的类型
	if len(types) == 0 && len(events) > 0 {
		// 按时间排序，获取最新的事件
		sort.Slice(events, func(i, j int) bool {
			return events[i].LastTime > events[j].LastTime // 按时间降序
		})
		if latestEvent := events[0]; latestEvent.InfoSecurity != "" {
			types = append(types, latestEvent.InfoSecurity)
		}
	}

	return types
}

// getRecentEvents 获取最近相关事件描述
func (ta *ThreatAnalyzer) getRecentEvents(events []sangforapi.SecurityEvent) []string {
	var recent []string

	// 按时间排序
	sort.Slice(events, func(i, j int) bool {
		return events[i].LastTime > events[j].LastTime // 降序
	})

	for _, event := range events {
		// 获取所有的最近事件
		recent = append(recent, event.Brief)
	}

	return recent
}

// generateSuggestions 生成建议措施
func (ta *ThreatAnalyzer) generateSuggestions(events []sangforapi.SecurityEvent, metrics RiskMetrics) []string {
	var suggestions []string
	assetRiskCount := make(map[string]int)    // 记录每个资产的高风险事件次数
	assetRiskMidCount := make(map[string]int) // 记录每个资产的中风险事件次数

	// 遍历历史事件，统计高风险事件
	for _, event := range events {
		if event.Reliability == 3 && event.IP != "" {
			assetRiskCount[event.IP]++
		} else if event.Reliability == 2 && event.IP != "" { // 统计中风险事件
			assetRiskMidCount[event.IP]++
		}
	}

	// 添加基于事件的最新解决方案
	if len(events) > 0 {
		lastEvent := events[len(events)-1]
		if lastEvent.Solution != "" {
			suggestions = append(suggestions, lastEvent.Solution)
		}
	}

	// 针对多个高风险事件集中于同一资产的情况，建议加强资产防护
	for asset, count := range assetRiskCount {
		if count > 1 { // 超过一个高风险事件
			suggestions = append(suggestions, fmt.Sprintf("检测到资产 '%s' 发生 %d 起高风险事件，建议加强该资产的防护", asset, count))
		}
	}

	// 针对多个高风险事件集中于同一资产的情况，建议加强资产防护
	for asset, count := range assetRiskMidCount {
		if count >= 3 { // 超过多个中风险事件
			suggestions = append(suggestions, fmt.Sprintf("检测到资产 '%s' 发生 %d 起普通风险事件，建议进行常规安全检查", asset, count))
		}
	}

	// 针对阶段进展，添加更通用的建议
	if metrics.StageProgression {
		suggestions = append(suggestions, "检测到攻击阶段进展，建议检查内网流量，排查潜在异常行为")
	}

	// 针对高风险统计情况的通用建议
	if metrics.HighRiskCount > 0 {
		suggestions = append(suggestions, "建议立即进行安全加固和漏洞修复")
	}

	// 去重处理
	suggestionSet := make(map[string]struct{})
	for _, suggestion := range suggestions {
		suggestionSet[suggestion] = struct{}{}
	}
	uniqueSuggestions := make([]string, 0, len(suggestionSet))
	for suggestion := range suggestionSet {
		uniqueSuggestions = append(uniqueSuggestions, suggestion)
	}

	return uniqueSuggestions
}

func (ta *ThreatAnalyzer) calculateRiskProbabilities(metrics RiskMetrics) [7][3]float64 {
	var dailyProbabilities [7][3]float64

	// 高风险计算
	highRiskBase := 0.0
	if metrics.HighRiskCount > 0 || metrics.StageProgression {
		highRiskBase = 0.6 // 高风险基准
	} else if metrics.AverageReliability >= 2.0 {
		highRiskBase = 0.3 // 中等高风险
	}

	// 中风险计算
	midRiskBase := 0.0
	if metrics.AverageReliability >= 1.5 {
		midRiskBase = 0.5
	} else {
		midRiskBase = 0.3
	}

	// 低风险为剩余补全
	for i := 0; i < 7; i++ {
		highRisk := math.Min(highRiskBase+float64(i)*0.02, 1.0)        // 随时间略微递增
		midRisk := math.Min(midRiskBase+float64(i)*0.01, 1.0-highRisk) // 受高风险影响的分布
		lowRisk := 1.0 - highRisk - midRisk                            // 补全低风险

		// 归一化处理确保和为1
		total := highRisk + midRisk + lowRisk
		highRisk /= total
		midRisk /= total
		lowRisk /= total

		dailyProbabilities[i] = [3]float64{highRisk, midRisk, lowRisk}
	}

	return dailyProbabilities
}

// 根据风险概率来判断未来 7 天内的风险高低
func calculateFutureRiskLevel(probability float64) int {
	switch {
	case probability < 0.3: // 低于30%视为低风险
		return 0
	case probability < 0.7: // 30%-70%视为中风险
		return 1
	default: // 70%以上视为高风险
		return 2
	}
}

// getWarningEvent 获取威胁预警事件并进行分析
// 优化版本：增加了性能监控、错误处理和数据安全性检查
func getWarningEvent(day int) (err error) {
	startTime := time.Now()

	if day == 0 {
		day = 30
	}

	// 获取数据库内最近 N天内的数据
	dbSecurityEvent := global.GVA_DB.Model(&sangforapi.SecurityEvent{})
	dbThreatWarnings := global.GVA_DB.Model(&sangforapi.ThreatWarnings{})

	// 获取当前时间戳和 N 天前的时间戳
	day64 := int64(day)
	now := time.Now().Unix()
	thirtyDaysAgo := now - day64*24*60*60 // N天前的时间戳
	global.GVA_LOG.Info(fmt.Sprintf("预警时间范围: %v 到 %v", thirtyDaysAgo, now))

	// 分页查询最近 N 天内的数据，避免内存溢出
	var events []sangforapi.SecurityEvent
	pageSize := 1000
	offset := 0

	for {
		var pageEvents []sangforapi.SecurityEvent
		err = dbSecurityEvent.Where("last_time >= ?", thirtyDaysAgo).
			Limit(pageSize).Offset(offset).Find(&pageEvents).Error
		if err != nil {
			global.GVA_LOG.Error("查询最近 N 天内的安全事件失败", zap.Error(err))
			return err
		}

		if len(pageEvents) == 0 {
			break
		}

		events = append(events, pageEvents...)
		offset += pageSize

		// 防止无限循环
		if len(pageEvents) < pageSize {
			break
		}
	}

	global.GVA_LOG.Info(fmt.Sprintf("查询到的事件数量:%v", len(events)))

	// 数据量检查
	if len(events) == 0 {
		global.GVA_LOG.Info("没有找到需要分析的安全事件")
		return nil
	}

	// 创建分析器实例
	analyzer := NewThreatAnalyzer(events, day)

	// 生成行为威胁预警
	warnings := analyzer.GenerateWarnings()
	global.GVA_LOG.Info(fmt.Sprintf("生成的预警数量:%v", len(warnings)))

	if len(warnings) == 0 {
		global.GVA_LOG.Info("未生成任何预警信息")
		return nil
	}

	var tws []sangforapi.ThreatWarnings
	for _, x := range warnings {
		predictedTypesJSON := safeMarshal(x.PredictedTypes, "PredictedTypes")
		recentEventsJSON := safeMarshal(x.RecentEvents, "RecentEvents")
		suggestionsJSON := safeMarshal(x.Suggestions, "Suggestions")

		hole := sangforapi.ThreatWarnings{
			IP:              x.IP,
			WarningTime:     x.WarningTime,
			EventSummary:    x.EventSummary,
			RiskLevel:       x.RiskLevel,
			Probability:     x.Probability,
			PredictedTypes:  predictedTypesJSON,
			RecentEvents:    recentEventsJSON,
			Suggestions:     suggestionsJSON,
			FutureRiskLevel: calculateFutureRiskLevel(x.Probability),
			RiskType:        x.RiskType,
		}
		tws = append(tws, hole)
	}

	// 开启事务
	tx := dbThreatWarnings.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			global.GVA_LOG.Error("事务因异常被回滚")
		}
	}()

	// 使用DELETE替代TRUNCATE，更安全
	if err := tx.Where("1 = 1").Delete(&sangforapi.ThreatWarnings{}).Error; err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("清空威胁预警记录失败", zap.Error(err))
		return err
	}

	// 分批插入新数据
	batchSize := 100
	for i := 0; i < len(tws); i += batchSize {
		end := i + batchSize
		if end > len(tws) {
			end = len(tws)
		}
		if err := tx.CreateInBatches(tws[i:end], batchSize).Error; err != nil {
			tx.Rollback()
			global.GVA_LOG.Error("批量插入威胁预警失败", zap.Error(err))
			return err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("事务提交失败", zap.Error(err))
		return err
	}

	// 注意：历史数据清理由专门的定时任务负责（每天凌晨2点执行）
	// 这里不再进行删除操作，避免与定时任务冲突

	elapsed := time.Since(startTime)
	global.GVA_LOG.Info(fmt.Sprintf("威胁预警记录成功保存，耗时: %v", elapsed))
	return nil
}

func safeMarshal(data interface{}, fieldName string) json.RawMessage {
	if data == nil {
		return json.RawMessage("[]")
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("序列化 %s 出错: %v", fieldName, data), zap.Error(err))
		return json.RawMessage("[]")
	}
	return jsonData
}

// 每天12点运行一次，获取最终 30天的所有数据然后进行分析
func RunGetWarningEvent() {
	// 运行一次，获取威胁预警, 默认为 30 天
	err := getWarningEvent(30)
	if err != nil {
		global.GVA_LOG.Error("循环获取威胁预警失败: %v\n", zap.Error(err))
	}
}

// 调用这个函数来启动 cron 调度任务
func StartCronJob() {
	// 创建一个新的 Cron 调度器
	c := cron.New(cron.WithLocation(time.Local)) // 使用本地时间（北京时间）
	// 添加每天12点执行的任务
	_, err := c.AddFunc("0 0 * * *", func() {
		global.GVA_LOG.Info("执行预警检测定时调度任务\n")
		RunGetWarningEvent()
	})
	if err != nil {
		global.GVA_LOG.Error("无法添加定时任务: %v\n", zap.Error(err))
		return
	}
	// 启动 cron 调度器
	c.Start()

	// 保持主线程运行，以便 cron 调度器能够继续工作
	select {}
}
